# stack_analyzer/file_triage_agent.py
import logging
import os
import fnmatch
import json
from typing import List, Dict

# --- Optional Imports for AI and Environment Variables ---
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    genai = None

try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

logger = logging.getLogger(__name__)

# Fallback database for rule-based analysis when AI is not available
FILE_FINGERPRINTS = {
    "Project Documentation": {"files": ["README.md", "readme.md", "Readme.md"]},
    "NPM Package Manager Definition": {"files": ["package.json"]},
    "Maven Project Definition": {"files": ["pom.xml"]},
    "Gradle Project Definition": {"files": ["build.gradle", "build.gradle.kts"]},
    "Python Pip Dependencies": {"files": ["requirements.txt", "requirements-dev.txt", "requirements-test.txt"]},
    "Python Poetry Project": {"files": ["pyproject.toml"]},
    "Python Application Entry Point": {"files": ["app.py", "main.py", "run.py", "server.py"]},
    ".NET Project File": {"wildcards": ["*.csproj", "*.fsproj"]},
    "Go Module Definition": {"files": ["go.mod"]},
    "Go Application Entry Point": {"files": ["main.go"]},
    "Containerization Definition": {"files": ["Dockerfile", "docker-compose.yml", "docker-compose.yaml"]},
    "GitHub Actions Workflow": {"path_wildcards": [".github/workflows/*.yml", ".github/workflows/*.yaml"]},
    "Terraform Configuration": {"wildcards": ["*.tf"]},
    "SQL Schema or Migration": {"wildcards": ["*.sql"]},
    "Nginx Configuration": {"files": ["nginx.conf"]},
    "Database Configuration": {"files": ["database.php", "database.py", "database.js", "db.php", "db.py", "db.js", "config/database.php"]},
    "Configuration Files": {"wildcards": ["*.config", "*.conf", "*.ini", "*.env", "*.yaml", "*.yml"]},
    "JavaScript/Node.js Entry Points": {"files": ["index.js", "app.js", "server.js", "main.js"]},
    "React/Vue Entry Points": {"files": ["main.jsx", "main.tsx", "index.jsx", "index.tsx", "App.jsx", "App.tsx"]},
    "Build Configuration": {"files": ["vite.config.js", "webpack.config.js", "rollup.config.js", "tsconfig.json"]},
    "PHP Entry Points": {"files": ["index.php", "app.php", "bootstrap.php"]},
    "Laravel Configuration": {"files": ["artisan", "composer.json"]},
    "Django Configuration": {"files": ["manage.py", "settings.py", "wsgi.py"]},
    "Flask Configuration": {"files": ["app.py", "run.py", "wsgi.py"]},
}

class FileTriageAgent:
    """
    Identifies critical files in a repository structure that are worth
    reading for a deeper analysis using AI reasoning or fallback rule-based approach.
    """
    def __init__(self, tree_nodes: list, use_ai: bool = True):
        """
        Initializes the FileTriageAgent.

        Args:
            tree_nodes (list): A list of dictionaries representing the file tree.
            use_ai (bool): Whether to use AI reasoning for file selection.
        """
        logger.info("Initializing FileTriageAgent...")
        self.nodes = tree_nodes
        self.all_file_paths: List[str] = []
        self.gemini_model = None

        logger.info(f"AI usage requested: {use_ai}")
        if use_ai and GEMINI_AVAILABLE:
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                try:
                    genai.configure(api_key=api_key)
                    self.gemini_model = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
                    logger.info("Gemini model initialized successfully for file triage.")
                except Exception as e:
                    logger.error(f"Failed to initialize Gemini: {e}")
            else:
                logger.warning("GEMINI_API_KEY not found. Using rule-based file selection.")
        elif use_ai and not GEMINI_AVAILABLE:
            logger.warning("AI requested but google-generativeai not installed. Using rule-based selection.")

        logger.info(f"FileTriageAgent initialized. AI analysis is {'ENABLED' if self.gemini_model else 'DISABLED'}.")

    def _get_all_file_paths(self):
        """Create a flat list of all file paths from the tree."""
        paths = []
        def traverse(nodes, current_path=""):
            for node in nodes:
                path = os.path.join(current_path, node['name']).replace("\\", "/")
                if node.get('type') == 'file':
                    paths.append(path)
                elif node.get("type") == "directory" and "children" in node:
                    traverse(node["children"], path)
        traverse(self.nodes, "")
        self.all_file_paths = paths

    def identify_critical_files(self) -> Dict[str, List[Dict]]:
        """
        Identifies critical files using AI reasoning or fallback rule-based approach.

        Returns:
            A dictionary containing a list of files to read, with their paths and reasons.
        """
        logger.info("Starting critical file identification...")
        self._get_all_file_paths()

        if self.gemini_model:
            logger.info("Using AI-powered file selection...")
            try:
                return self._identify_files_with_ai()
            except Exception as e:
                logger.error(f"AI file selection failed: {e}. Falling back to rule-based approach.")
                return self._identify_files_with_rules()
        else:
            logger.info("Using rule-based file selection...")
            return self._identify_files_with_rules()

    def _identify_files_with_ai(self) -> Dict[str, List[Dict]]:
        """Uses AI reasoning to identify critical files to read."""
        prompt = self._build_file_selection_prompt()

        logger.info("Sending file selection request to Gemini API...")
        try:
            response = self.gemini_model.generate_content(prompt)
            logger.info("Received response from Gemini API.")

            logger.debug(f"Raw Gemini response:\n---\n{response.text}\n---")

            return self._parse_ai_response(response.text)
        except Exception as e:
            logger.error(f"Error during Gemini API call: {e}", exc_info=True)
            raise e

    def _build_file_selection_prompt(self) -> str:
        """Builds a prompt focused on identifying files needed to RUN the application."""
        prompt = f"""You are a DevOps engineer analyzing a repository. Your primary mission is to identify all files necessary to understand how to build, configure, and run this application locally.

File Structure:
- {"\n- ".join(self.all_file_paths)}

Based on this file structure, identify all files that are essential for the setup and execution of the project. Prioritize files according to the following hierarchy:

**Priority 1: Top-Level Orchestration & Documentation**
- The main `docker-compose.yml` file if it exists.
- The root `README.md` file for setup instructions.

**Priority 2: Per-Service Build & Dependency Files**
- For **EACH** microservice directory identified (e.g., `auth_service`, `db_service`, etc.), you MUST include:
  - Its `Dockerfile`.
  - Its dependency management file (`package.json`, `requirements.txt`, `pom.xml`, etc.).

**Priority 3: Configuration & Initialization**
- Any database schema or initialization files (e.g., `init.sql`, migration files).
- Any central or service-specific configuration files that define environment variables, database connections, or ports (e.g., `config.js`, `.env.example`, `nginx.conf`).

**Priority 4: Service Entry Points**
- The main server file for each service (e.g., `server.js`, `app.py`) as it often reveals the port and start command.

**What to AVOID:**
- Do NOT select files that only contain business logic implementation (like specific controllers, helpers, or algorithms). We only care about what's needed to RUN the service, not how the code works internally.

Return ONLY a valid JSON object with this exact structure:
{{
  "files_to_read": [
    {{
      "path": "exact/file/path/from/structure",
      "reason": "A concise explanation of why this file is critical for building, configuring, or running the application."
    }}
  ]
}}
"""
        logger.debug(f"Generated file selection prompt:\n---\n{prompt}\n---")
        return prompt

    def _parse_ai_response(self, response_text: str) -> Dict[str, List[Dict]]:
        """Parses AI response to extract file selection."""
        try:
            import re
            # Find JSON object in the response
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)

                if isinstance(result, dict) and "files_to_read" in result:
                    files_to_read = result["files_to_read"]
                    if isinstance(files_to_read, list):
                        # Validate each file entry
                        valid_files = []
                        for file_entry in files_to_read:
                            if (isinstance(file_entry, dict) and
                                "path" in file_entry and
                                "reason" in file_entry):

                                # Check if the exact path exists
                                if file_entry["path"] in self.all_file_paths:
                                    valid_files.append(file_entry)
                                else:
                                    # Try to find a similar file (case-insensitive or partial match)
                                    suggested_path = self._find_similar_file(file_entry["path"])
                                    if suggested_path:
                                        logger.info(f"AI suggested '{file_entry['path']}' but found similar file '{suggested_path}'. Using the existing file.")
                                        file_entry["path"] = suggested_path
                                        valid_files.append(file_entry)
                                    else:
                                        logger.warning(f"AI suggested non-existent file: {file_entry['path']}")
                            else:
                                logger.warning(f"Invalid file entry format: {file_entry}")

                        logger.info(f"Successfully parsed AI response. Selected {len(valid_files)} files.")
                        return {"files_to_read": valid_files}

            logger.warning("Could not find valid JSON in AI response. Using fallback.")
            raise ValueError("Invalid AI response format")

        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse AI response: {e}")
            raise e

    def _find_similar_file(self, suggested_path: str) -> str:
        """Find a similar file path if the exact one doesn't exist."""
        import difflib

        # Try exact filename match in different directories
        suggested_filename = os.path.basename(suggested_path)
        for path in self.all_file_paths:
            if os.path.basename(path).lower() == suggested_filename.lower():
                return path

        # Try fuzzy matching on full paths
        close_matches = difflib.get_close_matches(
            suggested_path,
            self.all_file_paths,
            n=1,
            cutoff=0.6
        )

        return close_matches[0] if close_matches else None

    def _identify_files_with_rules(self) -> Dict[str, List[Dict]]:
        """Fallback rule-based file identification."""
        logger.info("Using rule-based file identification...")

        files_to_read = []
        added_paths = set()

        for path in self.all_file_paths:
            if path in added_paths:
                continue

            filename = os.path.basename(path)

            for reason, rules in FILE_FINGERPRINTS.items():
                matched = False

                if "files" in rules and filename in rules["files"]:
                    files_to_read.append({"path": path, "reason": reason})
                    added_paths.add(path)
                    matched = True

                elif "wildcards" in rules and any(fnmatch.fnmatch(filename, wc) for wc in rules["wildcards"]):
                    files_to_read.append({"path": path, "reason": reason})
                    added_paths.add(path)
                    matched = True

                elif "path_wildcards" in rules and any(fnmatch.fnmatch(path, wc) for wc in rules["path_wildcards"]):
                    files_to_read.append({"path": path, "reason": reason})
                    added_paths.add(path)
                    matched = True

                if matched:
                    break

        logger.info(f"Rule-based identification found {len(files_to_read)} critical files.")
        return {"files_to_read": sorted(files_to_read, key=lambda x: x['path'])}